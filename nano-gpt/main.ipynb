!wget https://raw.githubusercontent.com/karpathy/char-rnn/master/data/tinyshakespeare/input.txt

# 看看数据集内容
with open("input.txt", "r") as f:
    text = f.read()

print(text)

# 看看当前词汇表

chars = sorted(list(set(text)))
print(chars)
print(f"vocab size: {len(chars)}")

# tokenizer

token2str = {i: token for i, token in enumerate(chars)}
str2token = {token: i for i, token in enumerate(chars)}
print(f"{token2str=}")
print(f"{str2token=}")
encode = lambda s: [str2token[chr] for chr in s]
decode = lambda token_lis: "".join([token2str[num] for num in token_lis])


# test tokenizer
s = "hello world"
print(f"{encode(s)}")
print(f"{decode(encode(s))}")

# import torch
import torch

data = torch.tensor(encode(text))
print(data.shape, data.dtype)
print(data[:1000])

# 划分训练和测试

sep = int(0.9 * len(data))
train_data = data[:sep]
test_data = data[sep:]

print(f"{train_data=}")
print(f"{test_data=}")

# create get batch func
torch.manual_seed(1337)

batch_size = 4
block_size = 8


def get_batch(dataset: str):
    data = train_data if dataset == "train" else test_data
    ix = torch.randint(len(data) - block_size, (batch_size,))
    x = torch.stack([data[i : i + block_size] for i in ix])
    y = torch.stack([data[i + 1 : i + block_size + 1] for i in ix])
    return x, y


xb, yb = get_batch("train")
print(f"{xb=}")
print(f"{yb=}")

import torch
import torch.nn as nn
import torch.nn.functional as F

torch.manual_seed(1337)

class KarpathyBigramLanguageModel(nn.Module):
    def __init__(self, vocab_size):
        super().__init__()
        self.embedding_table=nn.Embedding(vocab_size,vocab_size)
    
    def forward(self,idx,targets):
        logits=self.embedding_table(idx) #shape: b x t x c
        if targets is None:
            loss=None
        else:
            b,t,c=logits.shape
            logits=logits.view(b*t,c)
            targets=targets.view(b*t)
            loss=F.cross_entropy(logits,targets)
        return logits,loss
    
    def generate(self,idx,max_tokens):
        """
        Args:
            idx (torch.Tensor): token sequence, 1 x t
            max_tokens (int): max tokens to generate
        """
        for _ in range(max_tokens):
            logits,loss=self(idx,None)
            final_logits=logits[:,-1,:] # 1 x c
            probs=F.softmax(final_logits,dim=-1) # 1 x c
            idx_next=torch.multinomial(probs,num_samples=1) # 1 x 1
            idx=torch.cat((idx,idx_next),dim=-1)
        return idx # 1 x t+max_token


vocab_size=len(chars)
m=KarpathyBigramLanguageModel(vocab_size)
logits,loss=m(xb,yb)
print(logits.shape)
print(loss)

print(decode(m.generate(idx=torch.zeros((1,1),dtype=torch.long),max_tokens=100)[0].tolist()))

optimizer=torch.optim.AdamW(m.parameters(),lr=1e-3)

batch_size=32

for steps in range(10000):
    xb,yb=get_batch('train')
    logits,loss=m(xb,yb)
    optimizer.zero_grad(set_to_none=True)
    loss.backward()
    optimizer.step()
    print(loss.item(),end='\r')

    

print(decode(m.generate(idx=torch.zeros((1,1),dtype=torch.long),max_tokens=500)[0].tolist()))