{"cells": [{"cell_type": "markdown", "id": "62b6670f", "metadata": {}, "source": ["## 实现bigram模型\n", "\n", "首先说说bigram模型.bigram模型就是,很经典的基于统计的二元的语言模型.他只计算每个token到所有的每个token的转移概率. \n", "\n", "在这里, 我们不再使用统计方法实现, 而是使用深度学习的方法去模拟这个方法.我们期望可以学习到一个token到token的转移概率矩阵. 有一个类型的网络天然适配我们的需求, 那就是nn.embedding()\n", "\n", "nn.embedding本质上就是一个lookup table,大小就是一个num_embeddings x embedding_dim的矩阵.代表了词表大小以及词嵌入维度.输入任意张量,输出一个每个元素替换为lookup table中对应向量的张量.\n"]}, {"cell_type": "code", "execution_count": null, "id": "3260c5de", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parameter containing:\n", "tensor([[ 0.1808, -0.0700, -0.3596, -0.9152,  0.6258],\n", "        [ 0.0255,  0.9545,  0.0643,  0.3612,  1.1679],\n", "        [-1.3499, -0.5102,  0.2360, -0.2398, -0.9211],\n", "        [ 1.5433,  1.3488, -0.1396,  0.2858,  0.9651],\n", "        [-2.0371,  0.4931,  1.4870,  0.5910,  0.1260],\n", "        [-1.5627, -1.1601, -0.3348,  0.4478, -0.8016],\n", "        [ 1.5236,  2.5086, -0.6631, -0.2513,  1.3514],\n", "        [-0.2759, -1.5108,  2.1048,  2.7630, -1.7465],\n", "        [ 0.4109, -0.2422,  0.8212, -0.2115,  0.7789],\n", "        [ 1.5333,  1.6097, -0.4032, -0.2749,  1.4738]], requires_grad=True)\n", "tensor([[ 0.1808, -0.0700, -0.3596, -0.9152,  0.6258],\n", "        [ 0.0255,  0.9545,  0.0643,  0.3612,  1.1679],\n", "        [-1.3499, -0.5102,  0.2360, -0.2398, -0.9211],\n", "        [ 1.5433,  1.3488, -0.1396,  0.2858,  0.9651],\n", "        [-2.0371,  0.4931,  1.4870,  0.5910,  0.1260]],\n", "       grad_fn=<EmbeddingBackward0>)\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "\n", "embedding = nn.Embedding(10, 5)  # 分别代表词表大小(),嵌入的维度\n", "print(embedding.weight)\n", "# 一个代表了一句话的张量,元素的值为token id\n", "sentence = torch.tensor([0, 1, 2, 3, 4])\n", "output = embedding(sentence)\n", "print(output)"]}, {"cell_type": "markdown", "id": "135dbf8c", "metadata": {}, "source": ["我们将embedding dim设置为词表大小, 那么这个时候我们可以用这个embedding矩阵表示转移分数,意思是每个token有多大的概率转移到另一个位置的token上."]}, {"cell_type": "code", "execution_count": null, "id": "a4d20a55", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["转移分数矩阵:\n", "Parameter containing:\n", "tensor([[-0.0514, -0.0646, -0.4970,  0.4658, -0.2573],\n", "        [-1.0673,  0.8353, -1.9560,  0.2228,  0.1073],\n", "        [ 1.2463,  1.2460,  0.3534,  0.9425, -1.6669],\n", "        [-1.3651, -0.1655, -1.9446,  0.0610, -0.2379],\n", "        [ 1.9020, -1.1763, -0.1772, -0.1334,  0.2940]], requires_grad=True)\n"]}], "source": ["import torch.nn as nn\n", "\n", "vocab_size=5 # 假设词表大小是5,方便演示\n", "\n", "transfer=nn.Embedding(5,5)\n", "print(f'转移分数矩阵:\\n{transfer.weight}')"]}, {"cell_type": "code", "execution_count": null, "id": "55d11537", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(tensor([4, 4]), tensor([0, 0]))\n"]}], "source": ["# 设计一段文本,作为数据集\n", "data=[0,1,2,3,4,0,1,2,3,4,0,1,2,3,4]\n", "\n", "batch_size=2\n", "# 设计一个函数,从数据集中随机采样一个batch\n", "def get_batch():\n", "    ix=torch.randint(0,len(data)-1,[batch_size])\n", "    x=torch.tensor([data[i] for i in ix],)\n", "    y=torch.tensor([data[i+1] for i in ix],)\n", "    return x,y\n", "\n", "print(get_batch())"]}, {"cell_type": "code", "execution_count": null, "id": "a3ef6f0b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[ 1.2463,  1.2460,  0.3534,  0.9425, -1.6669],\n", "        [-0.0514, -0.0646, -0.4970,  0.4658, -0.2573]],\n", "       grad_fn=<EmbeddingBackward0>)\n", "tensor(1.5567, grad_fn=<NllLossBackward0>)\n"]}], "source": ["# 测试一下loss计算\n", "import torch.nn.functional as F\n", "x,y=get_batch()\n", "output=transfer(x)\n", "print(output)\n", "loss=F.cross_entropy(output,y)\n", "print(loss)\n", "\n", "# ok, 没问题, 写一下网络结构"]}, {"cell_type": "code", "execution_count": null, "id": "01711ae3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MyBigramModel(\n", "  (embedding): Embedding(5, 5)\n", ")\n"]}], "source": ["# 定义,测试一下网络\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "\n", "class MyBigramModel(nn.Module):\n", "    def __init__(self,vocab_size):\n", "        super().__init__()\n", "        self.embedding=nn.Embedding(vocab_size,vocab_size)\n", "    \n", "    def forward(self,x,y):\n", "        y_=self.embedding(x)\n", "        loss=F.cross_entropy(y_,y)\n", "        return y_,loss\n", "\n", "\n", "mynet=MyBigramModel(vocab_size)\n", "print(mynet)"]}, {"cell_type": "code", "execution_count": null, "id": "e9cb896c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["logits=tensor([[-0.3949, -0.3296,  0.0080,  0.9262, -1.8846],\n", "        [ 0.1670,  0.4586, -1.7662, -1.6210,  1.1346]],\n", "       grad_fn=<EmbeddingBackward0>),loss=tensor(2.7760, grad_fn=<NllLossBackward0>)\n"]}], "source": ["# 看看网络能不能正常运行\n", "x,y=get_batch()\n", "logits,loss=mynet(x,y)\n", "print(f'{logits=},{loss=}')"]}, {"cell_type": "code", "execution_count": null, "id": "01cc93b5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["i=0,loss=tensor(2.0476, grad_fn=<NllLossBackward0>)\n", "i=100,loss=tensor(2.0040, grad_fn=<NllLossBackward0>)\n", "i=200,loss=tensor(2.5496, grad_fn=<NllLossBackward0>)\n", "i=300,loss=tensor(1.6620, grad_fn=<NllLossBackward0>)\n", "i=400,loss=tensor(1.6534, grad_fn=<NllLossBackward0>)\n", "i=500,loss=tensor(1.5647, grad_fn=<NllLossBackward0>)\n", "i=600,loss=tensor(2.3314, grad_fn=<NllLossBackward0>)\n", "i=700,loss=tensor(1.4663, grad_fn=<NllLossBackward0>)\n", "i=800,loss=tensor(1.3899, grad_fn=<NllLossBackward0>)\n", "i=900,loss=tensor(1.1711, grad_fn=<NllLossBackward0>)\n", "i=1000,loss=tensor(1.0710, grad_fn=<NllLossBackward0>)\n", "i=1100,loss=tensor(0.9529, grad_fn=<NllLossBackward0>)\n", "i=1200,loss=tensor(1.5781, grad_fn=<NllLossBackward0>)\n", "i=1300,loss=tensor(1.5989, grad_fn=<NllLossBackward0>)\n", "i=1400,loss=tensor(1.5221, grad_fn=<NllLossBackward0>)\n", "i=1500,loss=tensor(2.0166, grad_fn=<NllLossBackward0>)\n", "i=1600,loss=tensor(1.2921, grad_fn=<NllLossBackward0>)\n", "i=1700,loss=tensor(0.6980, grad_fn=<NllLossBackward0>)\n", "i=1800,loss=tensor(1.7332, grad_fn=<NllLossBackward0>)\n", "i=1900,loss=tensor(1.1103, grad_fn=<NllLossBackward0>)\n", "i=2000,loss=tensor(1.0528, grad_fn=<NllLossBackward0>)\n", "i=2100,loss=tensor(0.4862, grad_fn=<NllLossBackward0>)\n", "i=2200,loss=tensor(0.4539, grad_fn=<NllLossBackward0>)\n", "i=2300,loss=tensor(0.9301, grad_fn=<NllLossBackward0>)\n", "i=2400,loss=tensor(0.3912, grad_fn=<NllLossBackward0>)\n", "i=2500,loss=tensor(0.3795, grad_fn=<NllLossBackward0>)\n", "i=2600,loss=tensor(0.3940, grad_fn=<NllLossBackward0>)\n", "i=2700,loss=tensor(0.3307, grad_fn=<NllLossBackward0>)\n", "i=2800,loss=tensor(0.3514, grad_fn=<NllLossBackward0>)\n", "i=2900,loss=tensor(0.2838, grad_fn=<NllLossBackward0>)\n", "i=3000,loss=tensor(0.6747, grad_fn=<NllLossBackward0>)\n", "i=3100,loss=tensor(0.2813, grad_fn=<NllLossBackward0>)\n", "i=3200,loss=tensor(0.2931, grad_fn=<NllLossBackward0>)\n", "i=3300,loss=tensor(0.5018, grad_fn=<NllLossBackward0>)\n", "i=3400,loss=tensor(0.4493, grad_fn=<NllLossBackward0>)\n", "i=3500,loss=tensor(0.5007, grad_fn=<NllLossBackward0>)\n", "i=3600,loss=tensor(0.1980, grad_fn=<NllLossBackward0>)\n", "i=3700,loss=tensor(0.2555, grad_fn=<NllLossBackward0>)\n", "i=3800,loss=tensor(0.1933, grad_fn=<NllLossBackward0>)\n", "i=3900,loss=tensor(0.2377, grad_fn=<NllLossBackward0>)\n", "i=4000,loss=tensor(0.1644, grad_fn=<NllLossBackward0>)\n", "i=4100,loss=tensor(0.3012, grad_fn=<NllLossBackward0>)\n", "i=4200,loss=tensor(0.1576, grad_fn=<NllLossBackward0>)\n", "i=4300,loss=tensor(0.1790, grad_fn=<NllLossBackward0>)\n", "i=4400,loss=tensor(0.2559, grad_fn=<NllLossBackward0>)\n", "i=4500,loss=tensor(0.2289, grad_fn=<NllLossBackward0>)\n", "i=4600,loss=tensor(0.1235, grad_fn=<NllLossBackward0>)\n", "i=4700,loss=tensor(0.1554, grad_fn=<NllLossBackward0>)\n", "i=4800,loss=tensor(0.1131, grad_fn=<NllLossBackward0>)\n", "i=4900,loss=tensor(0.1050, grad_fn=<NllLossBackward0>)\n", "i=5000,loss=tensor(0.0883, grad_fn=<NllLossBackward0>)\n", "i=5100,loss=tensor(0.0961, grad_fn=<NllLossBackward0>)\n", "i=5200,loss=tensor(0.0904, grad_fn=<NllLossBackward0>)\n", "i=5300,loss=tensor(0.1527, grad_fn=<NllLossBackward0>)\n", "i=5400,loss=tensor(0.0826, grad_fn=<NllLossBackward0>)\n", "i=5500,loss=tensor(0.1363, grad_fn=<NllLossBackward0>)\n", "i=5600,loss=tensor(0.1286, grad_fn=<NllLossBackward0>)\n", "i=5700,loss=tensor(0.0743, grad_fn=<NllLossBackward0>)\n", "i=5800,loss=tensor(0.0660, grad_fn=<NllLossBackward0>)\n", "i=5900,loss=tensor(0.1042, grad_fn=<NllLossBackward0>)\n", "i=6000,loss=tensor(0.0651, grad_fn=<NllLossBackward0>)\n", "i=6100,loss=tensor(0.0534, grad_fn=<NllLossBackward0>)\n", "i=6200,loss=tensor(0.0893, grad_fn=<NllLossBackward0>)\n", "i=6300,loss=tensor(0.0483, grad_fn=<NllLossBackward0>)\n", "i=6400,loss=tensor(0.0497, grad_fn=<NllLossBackward0>)\n", "i=6500,loss=tensor(0.0439, grad_fn=<NllLossBackward0>)\n", "i=6600,loss=tensor(0.0723, grad_fn=<NllLossBackward0>)\n", "i=6700,loss=tensor(0.0398, grad_fn=<NllLossBackward0>)\n", "i=6800,loss=tensor(0.0421, grad_fn=<NllLossBackward0>)\n", "i=6900,loss=tensor(0.0540, grad_fn=<NllLossBackward0>)\n", "i=7000,loss=tensor(0.0861, grad_fn=<NllLossBackward0>)\n", "i=7100,loss=tensor(0.0490, grad_fn=<NllLossBackward0>)\n", "i=7200,loss=tensor(0.0312, grad_fn=<NllLossBackward0>)\n", "i=7300,loss=tensor(0.0534, grad_fn=<NllLossBackward0>)\n", "i=7400,loss=tensor(0.0527, grad_fn=<NllLossBackward0>)\n", "i=7500,loss=tensor(0.0469, grad_fn=<NllLossBackward0>)\n", "i=7600,loss=tensor(0.0374, grad_fn=<NllLossBackward0>)\n", "i=7700,loss=tensor(0.0358, grad_fn=<NllLossBackward0>)\n", "i=7800,loss=tensor(0.0371, grad_fn=<NllLossBackward0>)\n", "i=7900,loss=tensor(0.0417, grad_fn=<NllLossBackward0>)\n", "i=8000,loss=tensor(0.0313, grad_fn=<NllLossBackward0>)\n", "i=8100,loss=tensor(0.0207, grad_fn=<NllLossBackward0>)\n", "i=8200,loss=tensor(0.0198, grad_fn=<NllLossBackward0>)\n", "i=8300,loss=tensor(0.0277, grad_fn=<NllLossBackward0>)\n", "i=8400,loss=tensor(0.0399, grad_fn=<NllLossBackward0>)\n", "i=8500,loss=tensor(0.0192, grad_fn=<NllLossBackward0>)\n", "i=8600,loss=tensor(0.0165, grad_fn=<NllLossBackward0>)\n", "i=8700,loss=tensor(0.0347, grad_fn=<NllLossBackward0>)\n", "i=8800,loss=tensor(0.0261, grad_fn=<NllLossBackward0>)\n", "i=8900,loss=tensor(0.0250, grad_fn=<NllLossBackward0>)\n", "i=9000,loss=tensor(0.0238, grad_fn=<NllLossBackward0>)\n", "i=9100,loss=tensor(0.0318, grad_fn=<NllLossBackward0>)\n", "i=9200,loss=tensor(0.0212, grad_fn=<NllLossBackward0>)\n", "i=9300,loss=tensor(0.0179, grad_fn=<NllLossBackward0>)\n", "i=9400,loss=tensor(0.0111, grad_fn=<NllLossBackward0>)\n", "i=9500,loss=tensor(0.0190, grad_fn=<NllLossBackward0>)\n", "i=9600,loss=tensor(0.0235, grad_fn=<NllLossBackward0>)\n", "i=9700,loss=tensor(0.0152, grad_fn=<NllLossBackward0>)\n", "i=9800,loss=tensor(0.0161, grad_fn=<NllLossBackward0>)\n", "i=9900,loss=tensor(0.0098, grad_fn=<NllLossBackward0>)\n"]}], "source": ["# 简单写个训练看看\n", "import torch.optim as optim\n", "\n", "optimizer=optim.Adam(mynet.parameters(),lr=1e-3)\n", "for i in range(10000):\n", "    x,y=get_batch()\n", "    logits,loss=mynet(x,y)\n", "    optimizer.zero_grad()\n", "    loss.backward()\n", "    optimizer.step()\n", "    if i%100==0:\n", "        print(f'{i=},{loss=}')"]}, {"cell_type": "code", "execution_count": null, "id": "7afb34aa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parameter containing:\n", "tensor([[-3.6342,  2.9097, -3.2313, -2.3130, -5.1238],\n", "        [-3.5480, -3.2563,  1.9487, -5.3360, -2.5803],\n", "        [-3.3761, -3.0044, -2.4579,  3.0487, -3.0225],\n", "        [-3.0776, -4.0547, -3.4987, -3.0928,  2.7905],\n", "        [ 3.4417, -2.9522, -0.7499, -3.8631, -3.6285]], requires_grad=True)\n", "tensor([[1.4256e-03, 9.9078e-01, 2.1329e-03, 5.3427e-03, 3.2140e-04],\n", "        [4.0157e-03, 5.3756e-03, 9.7937e-01, 6.7180e-04, 1.0568e-02],\n", "        [1.6043e-03, 2.3265e-03, 4.0182e-03, 9.8977e-01, 2.2848e-03],\n", "        [2.8044e-03, 1.0556e-03, 1.8407e-03, 2.7619e-03, 9.9154e-01],\n", "        [9.8201e-01, 1.6416e-03, 1.4851e-02, 6.6026e-04, 8.3484e-04]],\n", "       grad_fn=<SoftmaxBackward0>)\n"]}], "source": ["# 看看权重,应该只有对应位置的最大.比如第一行应该是第二个最大, 第二行是第三个最大, 以此类推\n", "print(mynet.embedding.weight)\n", "print(<PERSON><PERSON>softmax(mynet.embedding.weight, dim=1))"]}, {"cell_type": "markdown", "id": "a0bcd8d5", "metadata": {}, "source": ["ok, 一个自己的bigram就写到这了, 让我们回头看小卡的bigram吧."]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}